import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'habits_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Habit Tracker',
      theme: ThemeData(
        colorScheme: const ColorScheme.light(
          primary: Color(0xFF4F46E5), // indigo-600
          onPrimary: Colors.white,
          secondary: Color(0xFF6366F1), // indigo-500
          surface: Colors.white,
          onSurface: Color(0xFF1F2937), // gray-800
          outline: Color(0xFFF3F4F6), // gray-100
          surfaceContainerHighest: Color(0xFFF9FAFB), // gray-50
        ),
        scaffoldBackgroundColor: const Color(0xFFF9FAFB), // gray-50
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: const Color(0xFF1F2937), // gray-800
          elevation: 0,
          surfaceTintColor: Colors.transparent,
          titleTextStyle: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1F2937), // gray-800
          ),
          iconTheme: const IconThemeData(
            color: Color(0xFF9CA3AF), // gray-400
          ),
        ),
        textTheme: GoogleFonts.interTextTheme().copyWith(
          bodyLarge: GoogleFonts.inter(
            color: const Color(0xFF1F2937), // gray-800
          ),
          bodyMedium: GoogleFonts.inter(
            color: const Color(0xFF1F2937), // gray-800
          ),
        ),
        useMaterial3: true,
      ),
      home: const HabitsScreen(),
    );
  }
}
