import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'habit.dart';
import 'date_scroller.dart';
import 'status_indicator.dart';

class HabitTile extends StatelessWidget {
  final Habit habit;
  final ScrollController scrollController;
  final Function(DateTime) onDateToggle;

  const HabitTile({
    super.key,
    required this.habit,
    required this.scrollController,
    required this.onDateToggle,
  });

  @override
  Widget build(BuildContext context) {
    final dates = DateScroller.generateDates();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFF3F4F6), // gray-100
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Habit name section with color indicator (3/8 of the width)
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: _getHabitColor(),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    habit.name,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF1F2937), // gray-700
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          // Daily status section (5/8 of the width)
          Expanded(
            flex: 5,
            child: ListView.builder(
              controller: scrollController,
              scrollDirection: Axis.horizontal,
              itemCount: dates.length,
              itemBuilder: (context, index) {
                final date = dates[index];
                final isCompleted = habit.isCompletedOnDate(date);
                final now = DateTime.now();
                final isToday =
                    date.year == now.year &&
                    date.month == now.month &&
                    date.day == now.day;
                final isPast = date.isBefore(
                  DateTime(now.year, now.month, now.day),
                );

                HabitStatus status;
                if (isCompleted) {
                  status = HabitStatus.completed;
                } else if (isPast) {
                  status = HabitStatus.missed;
                } else {
                  status = HabitStatus.pending;
                }

                return GestureDetector(
                  onTap: () => onDateToggle(date),
                  child: Container(
                    width: 60, // Fixed width to match DateTile alignment
                    padding: const EdgeInsets.symmetric(
                      horizontal: 4.0,
                      vertical: 8.0,
                    ),
                    decoration: BoxDecoration(
                      color: isToday
                          ? const Color(0xFFEEF2FF) // indigo-50
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(6),
                      border: isToday
                          ? null
                          : Border(
                              left: BorderSide(
                                color: const Color(0xFFF3F4F6), // gray-100
                                width: 1,
                              ),
                            ),
                    ),
                    child: Center(
                      child: StatusIndicator(status: status, isToday: isToday),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getHabitColor() {
    // Assign specific colors based on habit name to match the HTML reference exactly
    switch (habit.name) {
      case "Wake up early":
        return const Color(0xFF10B981); // green-500
      case "Cook healthy dinner":
        return const Color(0xFF22C55E); // green-400
      case "Write journal":
        return const Color(0xFFF87171); // red-400
      case "Track time":
        return const Color(0xFFFBBF24); // yellow-400
      case "Meditate":
        return const Color(0xFF3B82F6); // blue-500
      case "Run":
        return const Color(0xFF16A34A); // green-600
      case "Read books":
        return const Color(0xFF6366F1); // indigo-400
      case "Learn French":
        return const Color(0xFFF87171); // red-400
      case "Play chess":
        return const Color(0xFF60A5FA); // blue-400
      case "Practice guitar":
        return const Color(0xFF14B8A6); // teal-400
      case "Call a friend":
        return const Color(0xFF93C5FD); // blue-300
      default:
        return const Color(0xFF6B7280); // gray-500 for unknown habits
    }
  }
}
