import 'package:flutter/material.dart';
import 'habit.dart';
import 'date_scroller.dart';

class HabitTile extends StatelessWidget {
  final Habit habit;
  final ScrollController scrollController;
  final Function(DateTime) onDateToggle;

  const HabitTile({
    super.key,
    required this.habit,
    required this.scrollController,
    required this.onDateToggle,
  });

  @override
  Widget build(BuildContext context) {
    final dates = DateScroller.generateDates();

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Habit name section with color indicator
          SizedBox(
            width: 150,
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: _getHabitColor(context),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    habit.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // Daily status section
          Expanded(
            child: ListView.builder(
              controller: scrollController,
              scrollDirection: Axis.horizontal,
              itemCount: dates.length,
              itemBuilder: (context, index) {
                final date = dates[index];
                final isCompleted = habit.isCompletedOnDate(date);
                final now = DateTime.now();
                final isToday =
                    date.year == now.year &&
                    date.month == now.month &&
                    date.day == now.day;
                final isPast = date.isBefore(
                  DateTime(now.year, now.month, now.day),
                );

                return GestureDetector(
                  onTap: () => onDateToggle(date),
                  child: Container(
                    width: 60,
                    margin: const EdgeInsets.symmetric(horizontal: 4.0),
                    decoration: BoxDecoration(
                      color: _getBackgroundColor(
                        context,
                        isCompleted,
                        isToday,
                        isPast,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      border: isToday && !isCompleted
                          ? Border.all(
                              color: Theme.of(context).colorScheme.primary,
                              width: 2,
                            )
                          : null,
                    ),
                    child: Center(
                      child: Icon(
                        isCompleted ? Icons.check : Icons.close,
                        color: _getIconColor(
                          context,
                          isCompleted,
                          isToday,
                          isPast,
                        ),
                        size: 20,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor(
    BuildContext context,
    bool isCompleted,
    bool isToday,
    bool isPast,
  ) {
    if (isCompleted) {
      return Theme.of(context).colorScheme.primary.withValues(alpha: 0.2);
    }

    if (isToday) {
      return Theme.of(context).colorScheme.surface;
    }

    return Colors.transparent;
  }

  Color _getIconColor(
    BuildContext context,
    bool isCompleted,
    bool isToday,
    bool isPast,
  ) {
    if (isCompleted) {
      return Theme.of(context).colorScheme.primary;
    }

    if (isPast) {
      return Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3);
    }

    if (isToday) {
      return Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6);
    }

    return Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4);
  }

  Color _getHabitColor(BuildContext context) {
    // Assign different colors based on habit name for visual variety
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.purple,
      Colors.orange,
      Colors.red,
      Colors.teal,
      Colors.amber,
      Colors.indigo,
      Colors.pink,
      Colors.cyan,
      Colors.lime,
    ];

    // Use hash code to consistently assign colors
    final index = habit.name.hashCode.abs() % colors.length;
    return colors[index];
  }
}
