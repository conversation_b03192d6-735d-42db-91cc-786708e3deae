import 'package:flutter/material.dart';
import 'date_tile.dart';

class DateScroller extends StatefulWidget {
  final ScrollController? scrollController;
  final Function(DateTime)? getCompletionPercentage;

  const DateScroller({
    super.key,
    this.scrollController,
    this.getCompletionPercentage,
  });

  @override
  State<DateScroller> createState() => _DateScrollerState();

  // Static method to generate the same date range used by DateScroller
  static List<DateTime> generateDates() {
    final now = DateTime.now();
    final List<DateTime> dateList = [];

    // Generate the last 30 days
    for (int i = 29; i >= 0; i--) {
      dateList.add(DateTime(now.year, now.month, now.day - i));
    }

    return dateList;
  }
}

class _DateScrollerState extends State<DateScroller> {
  late DateTime selectedDate;
  late List<DateTime> dates;
  late ScrollController scrollController;

  @override
  void initState() {
    super.initState();
    selectedDate = DateTime.now();
    dates = _generateDates();
    scrollController = widget.scrollController ?? ScrollController();

    // Scroll to today's date after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedDate();
    });
  }

  @override
  void dispose() {
    // Only dispose if we created the controller ourselves
    if (widget.scrollController == null) {
      scrollController.dispose();
    }
    super.dispose();
  }

  List<DateTime> _generateDates() {
    return DateScroller.generateDates();
  }

  void _scrollToSelectedDate() {
    final selectedIndex = dates.indexWhere(
      (date) =>
          date.year == selectedDate.year &&
          date.month == selectedDate.month &&
          date.day == selectedDate.day,
    );

    if (selectedIndex != -1) {
      final scrollPosition = selectedIndex * 60.0; // Fixed width of 60
      scrollController.animateTo(
        scrollPosition,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      selectedDate = date;
    });
    _scrollToSelectedDate();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFF3F4F6), // gray-100
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Empty space to align with habit names (3/8 of the width)
          const Expanded(flex: 3, child: SizedBox()),
          // Date tiles section (5/8 of the width)
          Expanded(
            flex: 5,
            child: SizedBox(
              height: 60,
              child: ListView.builder(
                controller: scrollController,
                scrollDirection: Axis.horizontal,
                itemCount: dates.length,
                itemBuilder: (context, index) {
                  final date = dates[index];
                  final isSelected =
                      date.year == selectedDate.year &&
                      date.month == selectedDate.month &&
                      date.day == selectedDate.day;

                  final completionPercentage =
                      widget.getCompletionPercentage?.call(date) ?? 0.0;

                  return DateTile(
                    date: date,
                    isSelected: isSelected,
                    onTap: () => _onDateSelected(date),
                    completionPercentage: completionPercentage,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
