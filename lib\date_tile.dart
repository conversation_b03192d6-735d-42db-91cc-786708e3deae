import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class DateTile extends StatelessWidget {
  final DateTime date;
  final bool isSelected;
  final VoidCallback onTap;
  final double completionPercentage;

  const DateTile({
    super.key,
    required this.date,
    required this.isSelected,
    required this.onTap,
    required this.completionPercentage,
  });

  @override
  Widget build(BuildContext context) {
    // Get day of week abbreviation
    final dayOfWeek = _getDayOfWeekAbbreviation(date.weekday);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60, // Fixed width to match HabitTile alignment
        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFFEEF2FF) // indigo-50
              : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
          border: isSelected
              ? null
              : Border(
                  left: BorderSide(
                    color: const Color(0xFFF3F4F6), // gray-100
                    width: 1,
                  ),
                ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${completionPercentage.round()}%',
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? const Color(0xFF4F46E5) // indigo-600
                    : const Color(0xFF9CA3AF), // gray-400
              ),
            ),
            Text(
              dayOfWeek,
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? const Color(0xFF4F46E5) // indigo-600
                    : const Color(0xFF9CA3AF), // gray-400
              ),
            ),
            const SizedBox(height: 2),
            Text(
              date.day.toString(),
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? const Color(0xFF4F46E5) // indigo-600
                    : const Color(0xFF1F2937), // gray-800
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDayOfWeekAbbreviation(int weekday) {
    switch (weekday) {
      case 1:
        return 'MON';
      case 2:
        return 'TUE';
      case 3:
        return 'WED';
      case 4:
        return 'THU';
      case 5:
        return 'FRI';
      case 6:
        return 'SAT';
      case 7:
        return 'SUN';
      default:
        return '';
    }
  }
}
