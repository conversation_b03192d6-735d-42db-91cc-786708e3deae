import 'package:flutter/material.dart';

enum HabitStatus {
  completed,
  missed,
  pending,
}

class StatusIndicator extends StatelessWidget {
  final HabitStatus status;
  final bool isToday;

  const StatusIndicator({
    super.key,
    required this.status,
    this.isToday = false,
  });

  @override
  Widget build(BuildContext context) {
    switch (status) {
      case HabitStatus.completed:
        return Icon(
          Icons.check,
          size: 20,
          color: const Color(0xFF4F46E5), // indigo-500
        );
      case HabitStatus.missed:
        return Icon(
          Icons.close,
          size: 20,
          color: const Color(0xFFD1D5DB), // gray-300
        );
      case HabitStatus.pending:
        return Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isToday 
                ? const Color(0xFF4F46E5) // indigo-600
                : const Color(0xFFD1D5DB), // gray-300
              width: 1.5,
            ),
          ),
        );
    }
  }
}
