import 'package:flutter/material.dart';
import 'habit.dart';
import 'habit_tile.dart';
import 'date_scroller.dart';

class HabitsScreen extends StatefulWidget {
  const HabitsScreen({super.key});

  @override
  State<HabitsScreen> createState() => _HabitsScreenState();
}

class _HabitsScreenState extends State<HabitsScreen> {
  final ScrollController _scrollController = ScrollController();

  // Habits list matching the reference image
  List<Habit> habits = [
    Habit(name: "Wake up early"),
    Habit(name: "Cook healthy dinner"),
    Habit(name: "Write journal"),
    Habit(name: "Track time"),
    Habit(name: "Meditate"),
    Habit(name: "<PERSON>"),
    Habit(name: "Read books"),
    Habit(name: "Learn French"),
    Habit(name: "Play chess"),
    Habit(name: "Practice guitar"),
    Habit(name: "Call a friend"),
  ];

  @override
  void initState() {
    super.initState();
    _initializeSampleData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeSampleData() {
    // Add sample completion data matching the reference image pattern
    final today = DateTime.now();
    final dates = List.generate(
      5,
      (index) => today.subtract(Duration(days: index)),
    );

    // Wake up early - mostly completed
    habits[0].setCompletionForDate(dates[0], false); // today
    habits[0].setCompletionForDate(dates[1], true); // yesterday
    habits[0].setCompletionForDate(dates[2], false); // 2 days ago
    habits[0].setCompletionForDate(dates[3], true); // 3 days ago
    habits[0].setCompletionForDate(dates[4], true); // 4 days ago

    // Cook healthy dinner - mixed pattern
    habits[1].setCompletionForDate(dates[0], false);
    habits[1].setCompletionForDate(dates[1], false);
    habits[1].setCompletionForDate(dates[2], false);
    habits[1].setCompletionForDate(dates[3], true);
    habits[1].setCompletionForDate(dates[4], true);

    // Write journal - mixed pattern
    habits[2].setCompletionForDate(dates[0], false);
    habits[2].setCompletionForDate(dates[1], true);
    habits[2].setCompletionForDate(dates[2], true);
    habits[2].setCompletionForDate(dates[3], false);
    habits[2].setCompletionForDate(dates[4], true);

    // Track time - all completed
    habits[3].setCompletionForDate(dates[0], true);
    habits[3].setCompletionForDate(dates[1], true);
    habits[3].setCompletionForDate(dates[2], true);
    habits[3].setCompletionForDate(dates[3], true);
    habits[3].setCompletionForDate(dates[4], true);

    // Meditate - mostly completed
    habits[4].setCompletionForDate(dates[0], true);
    habits[4].setCompletionForDate(dates[1], false);
    habits[4].setCompletionForDate(dates[2], true);
    habits[4].setCompletionForDate(dates[3], true);
    habits[4].setCompletionForDate(dates[4], true);

    // Run - some completed (this would show numerical values in real app)
    habits[5].setCompletionForDate(dates[0], false);
    habits[5].setCompletionForDate(dates[1], true);
    habits[5].setCompletionForDate(dates[2], true);
    habits[5].setCompletionForDate(dates[3], false);
    habits[5].setCompletionForDate(dates[4], false);

    // Read books - mixed pattern
    habits[6].setCompletionForDate(dates[0], false);
    habits[6].setCompletionForDate(dates[1], true);
    habits[6].setCompletionForDate(dates[2], true);
    habits[6].setCompletionForDate(dates[3], true);
    habits[6].setCompletionForDate(dates[4], true);

    // Learn French - mostly completed
    habits[7].setCompletionForDate(dates[0], true);
    habits[7].setCompletionForDate(dates[1], true);
    habits[7].setCompletionForDate(dates[2], false);
    habits[7].setCompletionForDate(dates[3], true);
    habits[7].setCompletionForDate(dates[4], true);

    // Play chess - some completed
    habits[8].setCompletionForDate(dates[0], false);
    habits[8].setCompletionForDate(dates[1], false);
    habits[8].setCompletionForDate(dates[2], false);
    habits[8].setCompletionForDate(dates[3], true);
    habits[8].setCompletionForDate(dates[4], true);

    // Practice guitar - recent activity
    habits[9].setCompletionForDate(dates[0], true);
    habits[9].setCompletionForDate(dates[1], true);
    habits[9].setCompletionForDate(dates[2], false);
    habits[9].setCompletionForDate(dates[3], false);
    habits[9].setCompletionForDate(dates[4], false);

    // Call a friend - sporadic
    habits[10].setCompletionForDate(dates[0], true);
    habits[10].setCompletionForDate(dates[1], false);
    habits[10].setCompletionForDate(dates[2], false);
    habits[10].setCompletionForDate(dates[3], true);
    habits[10].setCompletionForDate(dates[4], false);
  }

  void habitDateToggled(int habitIndex, DateTime date) {
    setState(() {
      habits[habitIndex].toggleCompletionForDate(date);
    });
  }

  // Calculate completion percentage for a specific date
  double getCompletionPercentageForDate(DateTime date) {
    if (habits.isEmpty) return 0.0;

    int completedCount = 0;
    for (final habit in habits) {
      if (habit.isCompletedOnDate(date)) {
        completedCount++;
      }
    }

    return (completedCount / habits.length) * 100;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB), // gray-50
      body: Column(
        children: [
          // Header
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              top: 48, // Account for status bar
              bottom: 16,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Habits",
                  style: Theme.of(context).appBarTheme.titleTextStyle,
                ),
                Row(
                  children: [
                    Icon(
                      Icons.add,
                      color: Theme.of(context).appBarTheme.iconTheme?.color,
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.filter_list,
                      color: Theme.of(context).appBarTheme.iconTheme?.color,
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.more_vert,
                      color: Theme.of(context).appBarTheme.iconTheme?.color,
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Date Scroller
          Container(
            color: Colors.white,
            child: DateScroller(
              scrollController: _scrollController,
              getCompletionPercentage: getCompletionPercentageForDate,
            ),
          ),
          // Habits List
          Expanded(
            child: Container(
              color: Colors.white,
              child: ListView.builder(
                itemCount: habits.length,
                itemBuilder: (context, index) {
                  return HabitTile(
                    habit: habits[index],
                    scrollController: _scrollController,
                    onDateToggle: (date) => habitDateToggled(index, date),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
